# 个人作品集网站 - 项目架构PRD

## 一、项目概述

### 1.1 项目背景

本项目旨在构建一个现代化的个人作品展示平台，用于集中展示各类应用作品，建立个人技术品牌，并通过数据分析持续优化推广效果。

### 1.2 核心目标

- **展示作品**：系统化展示个人开发的各类应用（微信小程序、Chrome扩展、桌面应用等）
- **建立品牌**：通过专业的作品展示建立个人技术影响力
- **流量分析**：精准追踪不同渠道的访问流量，优化推广策略
- **技术沉淀**：打造可复用、易扩展的个人网站架构

### 1.3 项目编写说明
- 产品展示图尺寸：1312 × 736

### 1.4 技术选型

| 技术栈 | 选择 | 选型理由 |
|--------|------|----------|
| **核心框架** | Next.js 14 (App Router) | • 原生支持 SSG/ISR，SEO 优化极佳<br>• 与 Cloudflare Pages 完美兼容<br>• React 生态成熟，社区活跃 |
| **样式方案** | Tailwind CSS + shadcn/ui | • 原子化 CSS，开发效率高<br>• shadcn/ui 提供高质量组件<br>• 设计系统一致性强 |
| **数据管理** | 本地 JSON/MDX 文件 | • 无需数据库，部署简单<br>• Git 版本控制友好<br>• 内容编辑灵活 |
| **数据分析** | Cloudflare Analytics + GA4 | • 免费且隐私友好<br>• 数据维度丰富<br>• 支持自定义事件追踪 |
| **部署平台** | Cloudflare Pages | • 全球 CDN 加速<br>• 自动 CI/CD<br>• 免费额度充足 |

## 二、系统架构

### 2.1 架构图

```
┌─────────────────────────────────────────────────────────┐
│                     用户访问层                           │
│  (浏览器、搜索引擎爬虫、社交媒体预览)                    │
└────────────────────┬────────────────────────────────────┘
                     │
┌────────────────────▼────────────────────────────────────┐
│                  CDN 层 (Cloudflare)                     │
│         • 全球加速 • 缓存优化 • DDoS 防护                │
└────────────────────┬────────────────────────────────────┘
                     │
┌────────────────────▼────────────────────────────────────┐
│                应用层 (Next.js SSG)                      │
│   • 静态页面生成 • 路由管理 • API Routes                 │
└────────────────────┬────────────────────────────────────┘
                     │
┌────────────────────▼────────────────────────────────────┐
│                    数据层                                │
│     • MDX 文件 • JSON 配置 • 静态资源                    │
└──────────────────────────────────────────────────────────┘
```

### 2.2 目录结构

```
personal-portfolio/
├── app/                          # Next.js App Router
│   ├── layout.tsx               # 全局布局
│   ├── page.tsx                 # 首页
│   ├── apps/                    # 应用展示模块
│   │   ├── page.tsx            # 应用列表页
│   │   └── [slug]/             # 动态路由 - 应用详情
│   │       ├── page.tsx
│   │       └── opengraph-image.tsx  # OG 图片生成
│   ├── blog/                    # 博客模块
│   │   ├── page.tsx
│   │   └── [slug]/
│   │       └── page.tsx
│   ├── about/                   # 关于页面
│   │   └── page.tsx
│   ├── api/                     # API 路由
│   │   ├── track/              # 事件追踪
│   │   └── revalidate/         # ISR 重新验证
│   ├── sitemap.ts              # 站点地图生成
│   └── robots.ts               # robots.txt 配置
│
├── components/                   # 组件库
│   ├── ui/                     # 基础 UI 组件 (shadcn)
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   └── ...
│   ├── layout/                 # 布局组件
│   │   ├── header.tsx
│   │   ├── footer.tsx
│   │   ├── navigation.tsx
│   │   └── mobile-menu.tsx
│   ├── app/                    # 应用相关组件
│   │   ├── app-card.tsx        # 应用卡片
│   │   ├── app-grid.tsx        # 应用网格
│   │   ├── app-detail.tsx      # 应用详情
│   │   └── app-filter.tsx      # 应用筛选器
│   ├── common/                 # 通用组件
│   │   ├── seo.tsx            # SEO 元数据
│   │   ├── analytics.tsx      # 分析组件
│   │   ├── social-links.tsx   # 社交链接
│   │   └── theme-toggle.tsx   # 主题切换
│   └── mdx/                    # MDX 渲染组件
│       ├── mdx-components.tsx
│       └── code-block.tsx
│
├── lib/                         # 工具函数库
│   ├── utils.ts                # 通用工具函数
│   ├── analytics.ts            # 分析相关
│   ├── metadata.ts             # 元数据生成
│   ├── mdx.ts                  # MDX 处理
│   └── constants.ts            # 常量定义
│
├── data/                        # 数据文件
│   ├── apps/                   # 应用数据
│   │   ├── index.json         # 应用索引
│   │   └── *.mdx              # 应用详情文件
│   ├── blog/                   # 博客数据
│   │   └── *.mdx              # 博客文章
│   ├── profile.json            # 个人信息
│   └── social.json             # 社交账号
│
├── public/                      # 静态资源
│   ├── images/
│   │   ├── apps/              # 应用截图/图标
│   │   ├── blog/              # 博客配图
│   │   └── profile/           # 个人照片
│   ├── icons/                  # 网站图标
│   └── fonts/                  # 自定义字体
│
├── styles/                      # 样式文件
│   ├── globals.css             # 全局样式
│   └── markdown.css            # Markdown 样式
│
├── config/                      # 配置文件
│   ├── site.ts                 # 网站配置
│   ├── seo.ts                  # SEO 配置
│   └── nav.ts                  # 导航配置
│
├── types/                       # TypeScript 类型定义
│   ├── index.ts                # 通用类型
│   ├── app.ts                  # 应用类型
│   └── blog.ts                 # 博客类型
│
├── hooks/                       # 自定义 Hooks
│   ├── use-analytics.ts
│   └── use-theme.ts
│
└── scripts/                     # 构建脚本
    ├── generate-sitemap.js
    └── optimize-images.js
```

### 2.3 核心数据模型

```typescript
// types/app.ts
export interface App {
  // 基础信息
  id: string;
  slug: string;
  name: string;
  shortDescription: string;
  fullDescription: string;
  
  // 分类信息
  category: AppCategory;
  platform: Platform[];
  tags: string[];
  
  // 媒体资源
  icon: string;
  banner?: string;
  screenshots: Screenshot[];
  video?: string;
  
  // 链接
  links: {
    download?: string;
    demo?: string;
    github?: string;
    documentation?: string;
  };
  
  // 版本信息
  version: string;
  releaseDate: string;
  lastUpdateDate: string;
  
  // 统计数据
  stats?: {
    users?: number;
    downloads?: number;
    rating?: number;
    reviews?: number;
  };
  
  // 功能特性
  features: Feature[];
  techStack: string[];
  
  // SEO
  seo?: {
    title?: string;
    description?: string;
    keywords?: string[];
  };
  
  // 标记
  featured: boolean;
  status: 'active' | 'deprecated' | 'beta';
}

export enum AppCategory {
  WECHAT_MINIAPP = 'wechat-miniapp',
  CHROME_EXTENSION = 'chrome-extension',
  DESKTOP_APP = 'desktop-app',
  WEB_APP = 'web-app',
  MOBILE_APP = 'mobile-app',
}

export interface Screenshot {
  url: string;
  alt: string;
  caption?: string;
}

export interface Feature {
  title: string;
  description: string;
  icon?: string;
}
```

## 三、功能模块详细设计

### 3.1 SEO 优化模块

#### 3.1.1 元数据管理

```typescript
// lib/metadata.ts
export function generateMetadata(params: MetadataParams): Metadata {
  return {
    title: params.title,
    description: params.description,
    keywords: params.keywords,
    openGraph: {
      title: params.title,
      description: params.description,
      images: [params.ogImage],
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: params.title,
      description: params.description,
      images: [params.ogImage],
    },
    alternates: {
      canonical: params.url,
    },
  };
}
```

#### 3.1.2 结构化数据

```typescript
// components/common/structured-data.tsx
export function StructuredData({ type, data }: StructuredDataProps) {
  const jsonLd = generateJsonLd(type, data);
  
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
    />
  );
}
```

#### 3.1.3 性能优化策略

- **图片优化**：使用 Next.js Image 组件，自动 WebP 转换
- **字体优化**：字体子集化，使用 `next/font`
- **代码分割**：路由级别的自动代码分割
- **预连接**：关键域名预连接 (preconnect)
- **资源提示**：使用 prefetch/preload 优化关键资源

### 3.2 流量分析模块

#### 3.2.1 事件追踪系统

```typescript
// lib/analytics.ts
interface TrackingEvent {
  eventName: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  metadata?: Record<string, any>;
}

export class Analytics {
  // 追踪页面浏览
  static trackPageView(url: string) {
    // Cloudflare Analytics
    if (window.zaraz) {
      window.zaraz.track('page_view', { url });
    }
    
    // Google Analytics 4
    if (window.gtag) {
      window.gtag('event', 'page_view', {
        page_path: url,
      });
    }
  }
  
  // 追踪自定义事件
  static trackEvent(event: TrackingEvent) {
    // 实现细节...
  }
  
  // 追踪 UTM 参数
  static trackUTM() {
    const params = new URLSearchParams(window.location.search);
    const utmData = {
      source: params.get('utm_source'),
      medium: params.get('utm_medium'),
      campaign: params.get('utm_campaign'),
      term: params.get('utm_term'),
      content: params.get('utm_content'),
    };
    
    if (utmData.source) {
      this.trackEvent({
        eventName: 'utm_arrival',
        category: 'Traffic',
        action: 'UTM',
        metadata: utmData,
      });
    }
  }
}
```

#### 3.2.2 关键指标追踪

| 指标类型 | 追踪内容 | 用途 |
|---------|---------|------|
| **用户行为** | 页面停留时间、滚动深度、点击热图 | 优化页面布局和内容 |
| **转化漏斗** | 访问→查看应用→下载/访问链接 | 提高转化率 |
| **流量来源** | UTM 参数、Referrer、搜索关键词 | 优化推广渠道 |
| **技术指标** | Core Web Vitals、错误率、加载时间 | 提升用户体验 |

### 3.3 内容管理模块

#### 3.3.1 MDX 内容处理

```typescript
// lib/mdx.ts
import { compileMDX } from 'next-mdx-remote/rsc';

export async function getAppContent(slug: string) {
  const source = await readFile(`data/apps/${slug}.mdx`);
  
  const { content, frontmatter } = await compileMDX({
    source,
    options: {
      parseFrontmatter: true,
      mdxOptions: {
        remarkPlugins: [remarkGfm],
        rehypePlugins: [rehypeHighlight, rehypeSlug],
      },
    },
    components: MDXComponents,
  });
  
  return { content, metadata: frontmatter };
}
```

#### 3.3.2 内容版本控制

- 使用 Git 管理所有内容变更
- 支持草稿状态 (`draft: true`)
- 内容发布时间戳自动管理
- 支持内容回滚和历史查看

## 四、路由设计

### 4.1 路由结构

| 路径 | 页面 | 描述 |
|------|------|------|
| `/` | 首页 | 精选应用展示 + 个人简介 |
| `/apps` | 应用列表 | 所有应用的网格展示，支持筛选和搜索 |
| `/apps/[slug]` | 应用详情 | 单个应用的详细介绍页 |
| `/blog` | 博客列表 | 技术文章列表 |
| `/blog/[slug]` | 博客文章 | 单篇文章详情 |
| `/about` | 关于页面 | 详细的个人介绍和技能展示 |
| `/api/track` | API 路由 | 事件追踪端点 |
| `/api/revalidate` | API 路由 | ISR 重新验证端点 |

### 4.2 URL 设计原则

- **语义化**：URL 应清晰表达页面内容
- **简洁性**：避免冗长的参数和路径
- **持久性**：URL 一旦发布应保持稳定
- **SEO 友好**：使用连字符分隔，避免特殊字符

## 五、部署与运维

### 5.1 部署配置

#### 5.1.1 Cloudflare Pages 配置

```yaml
# .cloudflare/config.yaml
build:
  command: npm run build
  output: .next
  environment:
    - NODE_VERSION: 20

compatibility_date: 2024-01-01
compatibility_flags: ["nodejs_compat"]

routes:
  - pattern: /_next/static/*
    headers:
      Cache-Control: public, max-age=31536000, immutable
  - pattern: /images/*
    headers:
      Cache-Control: public, max-age=86400
  - pattern: /*
    headers:
      X-Frame-Options: DENY
      X-Content-Type-Options: nosniff
      Referrer-Policy: strict-origin-when-cross-origin
```

#### 5.1.2 环境变量配置

```bash
# .env.production
# 站点配置
NEXT_PUBLIC_SITE_URL=https://yourdomain.com
NEXT_PUBLIC_SITE_NAME="Your Name"

# 分析服务
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
NEXT_PUBLIC_CF_BEACON_TOKEN=xxxxxxxxxxxxx

# API 密钥（服务端使用）
REVALIDATE_SECRET=your-secret-key

# 功能开关
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_BLOG=true
```

### 5.2 监控与告警

#### 5.2.1 监控指标

- **性能监控**：LCP、FID、CLS、TTFB
- **可用性监控**：正常运行时间、错误率
- **流量监控**：PV、UV、会话时长
- **安全监控**：异常访问、攻击检测

#### 5.2.2 告警规则

```javascript
// 告警配置示例
const alertRules = [
  {
    metric: 'error_rate',
    threshold: 0.01, // 1%
    window: '5m',
    action: 'email',
  },
  {
    metric: 'response_time',
    threshold: 3000, // 3s
    window: '10m',
    action: 'slack',
  },
];
```

## 六、开发规范

### 6.1 代码规范

#### 6.1.1 TypeScript 配置

```json
{
  "compilerOptions": {
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  }
}
```

#### 6.1.2 ESLint 规则

```javascript
module.exports = {
  extends: [
    'next/core-web-vitals',
    'plugin:@typescript-eslint/recommended',
    'prettier',
  ],
  rules: {
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/no-explicit-any': 'warn',
    'react/display-name': 'off',
  },
};
```

### 6.2 Git 工作流

#### 6.2.1 分支策略

- `main`：生产环境分支
- `develop`：开发分支
- `feature/*`：功能分支
- `hotfix/*`：紧急修复分支

#### 6.2.2 提交规范

```bash
# 提交信息格式
<type>(<scope>): <subject>

# 示例
feat(apps): add new Chrome extension showcase
fix(seo): correct OG image generation
docs(readme): update deployment instructions
style(ui): improve mobile responsive layout
refactor(analytics): optimize tracking implementation
```

### 6.3 测试策略

#### 6.3.1 测试类型

| 测试类型 | 工具 | 覆盖范围 |
|---------|------|----------|
| 单元测试 | Jest + React Testing Library | 工具函数、组件逻辑 |
| 集成测试 | Playwright | 用户流程、页面交互 |
| 性能测试 | Lighthouse CI | Core Web Vitals |
| 可访问性测试 | axe-core | WCAG 2.1 AA 标准 |

#### 6.3.2 测试示例

```typescript
// __tests__/components/app-card.test.tsx
import { render, screen } from '@testing-library/react';
import { AppCard } from '@/components/app/app-card';

describe('AppCard', () => {
  it('should render app information correctly', () => {
    const mockApp = {
      name: 'Test App',
      description: 'Test Description',
      category: 'web-app',
    };
    
    render(<AppCard app={mockApp} />);
    
    expect(screen.getByText('Test App')).toBeInTheDocument();
    expect(screen.getByText('Test Description')).toBeInTheDocument();
  });
});
```

## 七、安全措施

### 7.1 安全配置

#### 7.1.1 内容安全策略 (CSP)

```typescript
// middleware.ts
export function middleware(request: NextRequest) {
  const response = NextResponse.next();
  
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' *.googletagmanager.com *.cloudflare.com; " +
    "style-src 'self' 'unsafe-inline'; " +
    "img-src 'self' data: https:; " +
    "font-src 'self' data:; " +
    "connect-src 'self' *.google-analytics.com *.cloudflare.com;"
  );
  
  return response;
}
```

#### 7.1.2 安全最佳实践

- 启用 HTTPS 强制跳转
- 实施速率限制防止 DDoS
- 定期更新依赖包
- 使用环境变量管理敏感信息
- 实施 CORS 策略
- 添加安全响应头

## 八、性能优化

### 8.1 优化策略

#### 8.1.1 前端优化

- **Bundle 优化**
  - Tree-shaking 移除未使用代码
  - 代码分割按需加载
  - 压缩 JS/CSS 文件
  
- **资源优化**
  - 图片格式自动选择 (WebP/AVIF)
  - 响应式图片加载
  - 字体预加载和显示优化
  
- **缓存策略**
  - 静态资源长期缓存
  - Service Worker 离线缓存
  - ISR 增量静态再生

#### 8.1.2 后端优化

- **渲染优化**
  - 静态生成 (SSG) 为主
  - 增量静态再生 (ISR) 为辅
  - Edge Runtime 优化
  
- **数据优化**
  - 数据预取和缓存
  - GraphQL 片段优化（如使用）
  - 分页和懒加载

### 8.2 性能监控

```typescript
// lib/performance.ts
export function measureWebVitals() {
  if (typeof window !== 'undefined') {
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS(sendToAnalytics);
      getFID(sendToAnalytics);
      getFCP(sendToAnalytics);
      getLCP(sendToAnalytics);
      getTTFB(sendToAnalytics);
    });
  }
}

function sendToAnalytics(metric: Metric) {
  // 发送到分析服务
  Analytics.trackEvent({
    eventName: 'web_vitals',
    category: 'Performance',
    action: metric.name,
    value: Math.round(metric.value),
  });
}
```

## 九、项目初始化步骤

### 9.1 环境准备

```bash
# 1. 检查 Node.js 版本（需要 18.17 或更高）
node --version

# 2. 安装 pnpm（推荐）或使用 npm/yarn
npm install -g pnpm
```

### 9.2 项目创建

```bash
# 1. 创建 Next.js 项目
pnpm create next-app@latest personal-portfolio \
  --typescript \
  --tailwind \
  --app \
  --src-dir=false \
  --import-alias="@/*"

# 2. 进入项目目录
cd personal-portfolio

# 3. 安装核心依赖
pnpm add @next/mdx @mdx-js/loader @mdx-js/react \
  gray-matter reading-time rehype-highlight \
  rehype-slug remark-gfm

# 4. 安装 UI 组件库
pnpm add lucide-react clsx tailwind-merge \
  @radix-ui/react-slot class-variance-authority

# 5. 配置 shadcn/ui
pnpm dlx shadcn-ui@latest init

# 6. 安装分析工具
pnpm add @vercel/analytics @vercel/speed-insights

# 7. 安装开发依赖
pnpm add -D @types/mdx @typescript-eslint/eslint-plugin \
  @typescript-eslint/parser eslint-config-prettier \
  prettier prettier-plugin-tailwindcss
```

### 9.3 配置文件设置

#### 9.3.1 Next.js 配置

```javascript
// next.config.mjs
import createMDX from '@next/mdx';

const withMDX = createMDX({
  extension: /\.mdx?$/,
  options: {
    remarkPlugins: [],
    rehypePlugins: [],
  },
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  pageExtensions: ['js', 'jsx', 'md', 'mdx', 'ts', 'tsx'],
  images: {
    formats: ['image/avif', 'image/webp'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  experimental: {
    mdxRs: true,
  },
};

export default withMDX(nextConfig);
```

#### 9.3.2 TypeScript 配置

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

### 9.4 初始化数据结构

```bash
# 创建必要的目录结构
mkdir -p data/apps data/blog public/images/apps public/images/blog
mkdir -p components/ui components/layout components/app components/common
mkdir -p lib hooks config types scripts

# 创建初始配置文件
touch data/profile.json data/social.json data/apps/index.json
touch config/site.ts config/seo.ts config/nav.ts
touch lib/utils.ts lib/analytics.ts lib/metadata.ts
```

### 9.4 本地运行

完成项目初始化后，可以使用以下命令在本地运行项目：

```bash
# 启动开发服务器
pnpm dev
# 或者使用 npm/yarn
npm run dev
yarn dev

# 开发服务器将在 http://localhost:3000 启动
```

#### 9.4.1 常用开发命令

```bash
# 启动开发服务器（默认端口 3000）
pnpm dev

# 指定端口启动
pnpm dev -- -p 3001

# 构建生产版本
pnpm build

# 预览生产构建
pnpm start

# 运行代码检查
pnpm lint

# 修复代码格式
pnpm lint --fix

# 运行类型检查
pnpm type-check
```

#### 9.4.2 开发环境配置

1. **环境变量设置**
   ```bash
   # 复制环境变量模板
   cp .env.example .env.local
   
   # 编辑环境变量
   # .env.local
   NEXT_PUBLIC_SITE_URL=http://localhost:3000
   NEXT_PUBLIC_SITE_NAME="Your Name"
   NEXT_PUBLIC_ENABLE_ANALYTICS=false
   ```

2. **首次运行检查清单**
   - [ ] Node.js 版本 >= 18.17
   - [ ] 依赖安装完成
   - [ ] 环境变量配置
   - [ ] 开发服务器正常启动
   - [ ] 页面可以正常访问

#### 9.4.3 开发工作流

```bash
# 1. 拉取最新代码
git pull origin main

# 2. 安装/更新依赖
pnpm install

# 3. 启动开发服务器
pnpm dev

# 4. 开始开发...

# 5. 提交代码前检查
pnpm lint
pnpm type-check
pnpm build
```

## 十、开发指南

### 10.1 添加新应用

1. **创建应用数据文件**
   ```bash
   # 在 data/apps/ 目录创建 MDX 文件
   touch data/apps/my-new-app.mdx
   添加新APP: 在 /data/apps/ 创建MDX文件，更新index.json
   修改个人信息: 编辑 /data/profile.json
   添加社交账号: 编辑 /data/social.json
   写博客文章: 在 /data/blog/ 添加MDX文件
   自定义组件: 在 /components/ 对应目录添加
   ```

2. **编写应用内容**
   ```mdx
   ---
   id: "my-new-app"
   slug: "my-new-app"
   name: "我的新应用"
   category: "web-app"
   description: "应用描述"
   releaseDate: "2024-01-01"
   featured: true
   ---
   
   ## 功能介绍
   
   应用的详细介绍内容...
   ```

3. **更新应用索引**
   ```json
   // data/apps/index.json
   {
     "apps": [
       {
         "id": "my-new-app",
         "slug": "my-new-app",
         // ... 其他字段
       }
     ]
   }
   ```

### 10.2 自定义主题

```css
/* styles/globals.css */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    /* ... 其他颜色变量 */
  }
  
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    /* ... 暗色模式变量 */
  }
}
```

### 10.3 添加新组件

```typescript
// components/ui/custom-component.tsx
import { cn } from '@/lib/utils';

interface CustomComponentProps {
  className?: string;
  children: React.ReactNode;
}

export function CustomComponent({ 
  className, 
  children 
}: CustomComponentProps) {
  return (
    <div className={cn('your-styles', className)}>
      {children}
    </div>
  );
}
```

## 十一、维护与更新

### 11.1 日常维护

- **每周任务**
  - 检查分析数据，优化低性能页面
  - 更新应用统计数据
  - 发布新的博客文章（如有）
  
- **每月任务**
  - 更新依赖包版本
  - 性能审计和优化
  - 备份重要数据
  
- **每季度任务**
  - 安全审计
  - SEO 效果评估
  - 用户反馈收集和改进

### 11.2 版本发布流程

1. **准备发布**
   ```bash
   # 创建发布分支
   git checkout -b release/v1.x.x
   
   # 更新版本号
   npm version patch/minor/major
   
   # 运行测试
   pnpm test
   pnpm build
   ```

2. **部署流程**
   - 合并到 main 分支
   - 自动触发 CI/CD
   - Cloudflare Pages 自动部署
   - 验证生产环境

3. **发布后检查**
   - 验证所有页面正常访问
   - 检查分析代码工作正常
   - 监控错误日志

## 十二、故障排查

### 12.1 常见问题

| 问题 | 可能原因 | 解决方案 |
|------|---------|----------|
| 构建失败 | 依赖版本冲突 | 清除 node_modules 和 lock 文件，重新安装 |
| 样式不生效 | Tailwind 配置问题 | 检查 content 配置是否包含所有文件 |
| MDX 渲染错误 | 语法错误或插件问题 | 检查 MDX 语法，更新相关插件 |
| 图片不显示 | 路径错误或配置问题 | 检查图片路径和 next.config.js 配置 |
| 分析不工作 | 环境变量未设置 | 确认环境变量正确配置 |

### 12.2 调试技巧

```typescript
// 开发环境调试助手
if (process.env.NODE_ENV === 'development') {
  console.log('Debug info:', {
    env: process.env.NODE_ENV,
    apiUrl: process.env.NEXT_PUBLIC_API_URL,
    // 其他调试信息
  });
}

// 性能调试
console.time('ComponentRender');
// 组件代码
console.timeEnd('ComponentRender');
```

## 十三、扩展计划

### 13.1 短期计划（1-3个月）

- [ ] 实现暗色模式切换
- [ ] 添加搜索功能
- [ ] 集成评论系统
- [ ] 优化移动端体验
- [ ] 添加 RSS 订阅

### 13.2 中期计划（3-6个月）

- [ ] 多语言支持（i18n）
- [ ] 添加 Newsletter 订阅
- [ ] 实现内容推荐算法
- [ ] 集成 AI 聊天助手
- [ ] 添加访客统计看板

### 13.3 长期计划（6-12个月）

- [ ] 开发配套的 CMS 系统
- [ ] 支持用户互动（点赞、收藏）
- [ ] 建立开发者社区
- [ ] 提供 API 接口
- [ ] 打造个人品牌体系

## 十四、参考资源

### 14.1 官方文档

- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [MDX Documentation](https://mdxjs.com/)
- [Cloudflare Pages Docs](https://developers.cloudflare.com/pages)

### 14.2 学习资源

- [Next.js Learn Course](https://nextjs.org/learn)
- [Tailwind CSS Best Practices](https://tailwindcss.com/docs/best-practices)
- [Web.dev Performance Guide](https://web.dev/performance)
- [SEO Starter Guide](https://developers.google.com/search/docs/fundamentals/seo-starter-guide)

### 14.3 工具推荐

- **开发工具**
  - VS Code + 推荐扩展
  - React Developer Tools
  - Tailwind CSS IntelliSense
  
- **测试工具**
  - Lighthouse
  - PageSpeed Insights
  - GTmetrix
  
- **设计工具**
  - Figma
  - Excalidraw
  - Carbon (代码截图)

## 十五、更新日志

### Version 1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础功能模块
- 集成分析系统

---

## 附录 A：快速启动清单

- [ ] 克隆或创建项目
- [ ] 安装依赖包
- [ ] 配置环境变量
- [ ] 添加个人信息
- [ ] 添加第一个应用
- [ ] 本地测试运行
- [ ] 部署到 Cloudflare Pages
- [ ] 配置自定义域名
- [ ] 设置分析服务
- [ ] 发布上线

## 更多项目介绍：
项目结构概览
这是一个典型的 Next.js 项目结构。Next.js 是一个流行的 React 框架，用于构建现代 Web 应用程序。

核心应用代码在 app/ 和 components/ 目录中。
数据和内容存放在 data/ 目录中。
配置文件位于 config/ 和根目录。
公共静态资源（如图片）在 public/ 目录中。
文件夹和文件详解
app/
这个目录是 Next.js 13+ 的核心，负责应用的路由和页面。

app/layout.tsx: 这是最顶层的布局文件，包含了网站的 <html> 和 <body> 标签。如果你想修改所有页面的通用背景、字体或者添加全局的 meta 标签，可以编辑这个文件。
app/page.tsx: 这是网站的主页（/ 路径）。如果你想修改首页的内容，这里是你要找的文件。
app/globals.css: 全局 CSS 样式文件。你可以在这里定义整个网站通用的样式。
app/favicon.ico: 网站在浏览器标签页上显示的小图标。
app/apps/: 这个文件夹定义了 /apps 相关的路由。
app/apps/page.tsx: /apps 路径的页面，可能会展示一个应用列表。
app/apps/[slug]/: 这是一个动态路由。[slug] 是一个占位符，代表任何一个应用的名称。例如，/apps/desktop-switcher 就会匹配到这个路由。
app/apps/[slug]/page.tsx: 这是单个应用详情页面的模板。当用户访问一个具体的应用页面时，这个文件会负责渲染页面内容。
维护指南：

要修改主页，请编辑 app/page.tsx。
要修改所有页面的共同布局（比如添加一个全局的导航栏或页脚），请编辑 app/layout.tsx。
要添加一个新的页面/路由，可以在 app/ 下创建一个新的文件夹和 page.tsx 文件。
要修改应用列表页，请编辑 app/apps/page.tsx。
要修改应用详情页的模板，请编辑 app/apps/[slug]/page.tsx。
components/
这个目录存放了所有可复用的 React 组件。把UI拆分成小组件是 React 开发的最佳实践。

components/ui/: 存放最基础的 UI 元素。
badge.tsx: 徽章组件。
button.tsx: 按钮组件。
card.tsx: 卡片组件。
components/layout/: 存放构成页面布局的组件。
header.tsx: 网站的页头/导航栏。
footer.tsx: 网站的页脚。
components/app/: 存放与“应用”功能相关的特定组件。
app-card.tsx: 用于在列表里显示单个应用的卡片。
featured-app.tsx: 用于在主页等地方展示特色应用的组件。
components/mdx/: 如果项目中使用 MDX（在 Markdown 中写 JSX），这里可能包含处理 MDX 内容的组件。
维护指南：

如果你想修改网站的导航栏，请编辑 components/layout/header.tsx。
如果你想修改网站的页脚，请编辑 components/layout/footer.tsx。
如果你想调整按钮、卡片等基础UI元素的样式或功能，请查看 components/ui/ 里的对应文件。
当你发现多个页面有重复的UI部分时，可以考虑把它提取成一个新的组件放到这个文件夹里。
data/
这个目录存放了网站使用的非代码数据，实现了代码和内容的分离。

data/profile.json: 存放个人信息的数据，比如你的名字、简介、社交链接等。
data/apps/: 存放所有应用项目的数据。
index.json: 可能是所有应用项目的列表索引，包含了每个应用的元数据（如标题、描述、图片链接等）。
desktop-switcher.mdx: 这是一个 MDX 文件，很可能包含了 "Desktop Switcher" 这个应用的详细介绍。使用 MDX 格式可以让你在写文章的同时，还能嵌入 React 组件。
维护指南：

要更新你的个人信息，请编辑 data/profile.json。
要添加、删除或修改一个应用项目，你需要：
1.
在 data/apps/index.json 中更新应用的条目。
2.
（如果需要）在 data/apps/ 目录下添加或修改对应的 .mdx 详细描述文件。
config/
存放项目的配置文件。

config/site.ts: 存放网站的全局配置，比如网站名称、描述、URL等。
维护指南：

要修改网站的名称或描述等元数据，请编辑 config/site.ts。
public/
这个目录用于存放静态资源，这里的文件可以被浏览器直接访问。

public/images/: 存放图片资源，比如你的头像 (avatar_me.png) 和项目截图。
public/file.svg, globe.svg, next.svg, vercel.svg, window.svg: 这些是项目中用到的 SVG 图标。
维护指南：

要添加新的图片，直接把图片文件放入 public/images/ 文件夹，然后在代码中通过 /images/your-image-name.png 这样的路径来使用它。
要更换头像，可以用新的图片替换 public/images/avatar_me.png。
lib/ 和 types/
这两个文件夹用于存放辅助代码。

lib/utils.ts: 存放通用的辅助函数，比如格式化日期、处理字符串等。
lib/analytics.ts: 可能用于集成网站分析工具（如 Google Analytics）。
types/index.ts: 存放自定义的 TypeScript 类型定义。这有助于代码的健壮性和可维护性。
维护指南：

如果你有一些在项目中多处使用的函数，可以把它们放到 lib/utils.ts 中。
如果你在项目中定义了一些复杂的数据结构（比如一个 App 或 Profile 对象），最好在 types/index.ts 中为它们创建类型定义。
根目录下的配置文件
这些是项目构建和开发环境的配置文件。通常你不需要经常修改它们，除非你要改变项目的构建方式或依赖库。

next.config.ts: Next.js 的主配置文件。
tailwind.config.ts: Tailwind CSS 的配置文件。如果你想自定义颜色、字体、间距等设计系统的变量，可以修改这里。
package.json: 定义了项目的依赖库和脚本命令（如 npm run dev）。
tsconfig.json: TypeScript 编译器配置。
.gitignore: 告诉 Git 哪些文件或文件夹不需要被版本控制。
README.md: 项目的说明文档。
总结
想改页面内容？ 去 app/ 目录找对应的 page.tsx，或者去 data/ 目录修改数据。
想改页面样式或组件？ 去 components/ 目录找对应的组件文件，或者修改 globals.css 和 tailwind.config.ts。
想添加一个项目？ 主要修改 data/apps/ 目录下的文件。
想改网站名字？ 去 config/site.ts。
想换图片？ 去 public/images/。
